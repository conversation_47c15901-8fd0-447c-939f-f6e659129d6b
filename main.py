"""
FastAPI application for Location API
"""
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
import logging

from models import APIResponse, LocationItem, LocationRequest, PosAPIResponse, PosItem, PosRequest, LoginResponse, LoginData
from database import db_manager
from config import SUCCESS_CODE, SUCCESS_MSG, ERROR_CODE, ERROR_MSG
import jwt
import hashlib
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Location API",
    description="API for managing location data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize database and import data on startup"""
    logger.info("Starting up application...")
    
    # Import data if database is empty
    locations_count = db_manager.get_locations_count()
    pos_count = db_manager.get_pos_count()
    branches_count = db_manager.get_branches_count()
    branch_province_count = db_manager.get_branch_province_count()
    ward_new_count = db_manager.get_ward_new_count()

    if locations_count == 0:
        logger.info("Locations database is empty, importing data...")
        success = db_manager.import_locations_data()
        if success:
            logger.info("Locations data import completed successfully")
        else:
            logger.error("Locations data import failed")
    else:
        logger.info(f"Database already contains {locations_count} location records")

    if pos_count == 0:
        logger.info("POS database is empty, importing data...")
        success = db_manager.import_pos_data()
        if success:
            logger.info("POS data import completed successfully")
        else:
            logger.error("POS data import failed")
    else:
        logger.info(f"Database already contains {pos_count} POS records")

    if branches_count == 0:
        logger.info("Branches database is empty, importing data...")
        success = db_manager.import_branches_data()
        if success:
            logger.info("Branches data import completed successfully")
        else:
            logger.error("Branches data import failed")
    else:
        logger.info(f"Database already contains {branches_count} branch records")

    if branch_province_count == 0:
        logger.info("Branch-province mapping is empty, importing data...")
        success = db_manager.import_branch_province_data()
        if success:
            logger.info("Branch-province mapping import completed successfully")
        else:
            logger.error("Branch-province mapping import failed")
    else:
        logger.info(f"Database already contains {branch_province_count} branch-province mappings")

    if ward_new_count == 0:
        logger.info("Ward mapping is empty, importing data...")
        success = db_manager.import_ward_new_data()
        if success:
            logger.info("Ward mapping import completed successfully")
        else:
            logger.error("Ward mapping import failed")
    else:
        logger.info(f"Database already contains {ward_new_count} ward mappings")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Location API is running", "status": "OK"}

@app.get("/api/login", response_model=LoginResponse)
async def login(
    account: str = Query(..., description="Account username"),
    passwd: str = Query(..., description="Account password")
):
    """
    Mock login API endpoint

    - **account**: Username for login
    - **passwd**: Password for login

    Returns a mock JWT token for testing purposes.
    """
    try:
        logger.info(f"Login attempt for account: {account}")

        # Mock authentication - accept any account/password for demo
        # In real implementation, you would validate against database
        valid_accounts = {
            "esb": "123456",
            "admin": "admin123",
            "test": "test123"
        }

        if account not in valid_accounts or valid_accounts[account] != passwd:
            return LoginResponse(
                data=LoginData(
                    accessToken="",
                    expiredTime=""
                ),
                errorCode="401",
                errorMsg="Invalid credentials"
            )

        # Generate mock JWT token
        secret_key = "mock_secret_key_for_demo"

        # Create payload
        now = datetime.now()
        expired_time = now + timedelta(hours=24)  # Token expires in 24 hours

        payload = {
            "account": account,
            "iat": int(now.timestamp()),
            "exp": int(expired_time.timestamp()),
            "mock": True
        }

        # Generate token
        token = jwt.encode(payload, secret_key, algorithm="HS256")

        # Format expired time to match the sample
        expired_time_str = expired_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "+07:00"

        logger.info(f"Login successful for account: {account}")

        return LoginResponse(
            data=LoginData(
                accessToken=token,
                expiredTime=expired_time_str
            ),
            errorCode="200",
            errorMsg="Success"
        )

    except Exception as e:
        logger.error(f"Error in login: {e}")
        return LoginResponse(
            data=LoginData(
                accessToken="",
                expiredTime=""
            ),
            errorCode="500",
            errorMsg=f"Internal server error: {str(e)}"
        )

@app.get("/api/getlocation", response_model=APIResponse)
async def get_location(
    AccessToken: Optional[str] = Query(None, description="Access token (not validated)"),
    LocationType: Optional[str] = Query(None, description="Location type: 0=Region, 1=Province, 2=District, 3=Ward"),
    UpdatedDate: Optional[str] = Query(None, description="Updated date in YYYYMMDD format")
):
    """
    Get location information based on location type and updated date
    
    - **AccessToken**: Token (not validated as per requirement)
    - **LocationType**: Type of location (0: Region, 1: Province, 2: District, 3: Ward)
    - **UpdatedDate**: Date filter in YYYYMMDD format
    """
    try:
        logger.info(f"Getting locations - Type: {LocationType}, Date: {UpdatedDate}")
        
        # Get locations from database
        locations_data = db_manager.get_locations(
            location_type=LocationType,
            updated_date=UpdatedDate
        )
        
        # Convert to LocationItem models
        location_items = [
            LocationItem(
                Id=loc['Id'],
                LocationType=loc['LocationType'],
                LocationCode=loc['LocationCode'],
                LocationDigitCode=loc['LocationDigitCode'],
                LocationName=loc['LocationName'],
                ParentCode=loc['ParentCode'],
                Status=loc['Status']
            )
            for loc in locations_data
        ]
        
        logger.info(f"Returning {len(location_items)} locations")
        
        return APIResponse(
            ErrorCode=SUCCESS_CODE,
            ErrorMsg=SUCCESS_MSG,
            Data=location_items
        )
        
    except Exception as e:
        logger.error(f"Error in get_location: {e}")
        return APIResponse(
            ErrorCode=ERROR_CODE,
            ErrorMsg=f"Error: {str(e)}",
            Data=[]
        )

@app.get("/api/stats")
async def get_stats():
    """Get database statistics"""
    try:
        total_locations = db_manager.get_locations_count()
        total_pos = db_manager.get_pos_count()
        total_branches = db_manager.get_branches_count()
        total_branch_province = db_manager.get_branch_province_count()
        total_pos_new = db_manager.get_pos_new_count()
        total_ward_new = db_manager.get_ward_new_count()

        # Get count by location type
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT locationType, COUNT(*)
            FROM locations
            GROUP BY locationType
            ORDER BY locationType
        """)
        type_counts = dict(cursor.fetchall())

        # Get count by POS status
        cursor.execute("""
            SELECT posStatus, COUNT(*)
            FROM pos
            GROUP BY posStatus
            ORDER BY posStatus
        """)
        pos_status_counts = dict(cursor.fetchall())

        # Get count by branch type
        cursor.execute("""
            SELECT branch_type, COUNT(*)
            FROM branches
            GROUP BY branch_type
            ORDER BY branch_type
        """)
        branch_type_counts = dict(cursor.fetchall())

        conn.close()

        return {
            "total_locations": total_locations,
            "total_pos": total_pos,
            "total_pos_new": total_pos_new,
            "total_branches": total_branches,
            "total_branch_province_mappings": total_branch_province,
            "total_ward_new": total_ward_new,
            "pos_mapping_difference": total_pos_new - 6085 if total_pos_new > 0 else 0,  # Difference between CSV and mapped POS
            "locations_by_type": {
                "0_regions": type_counts.get(0, 0),
                "1_provinces": type_counts.get(1, 0),
                "2_districts": type_counts.get(2, 0),
                "3_wards": type_counts.get(3, 0)
            },
            "pos_by_status": pos_status_counts,
            "branches_by_type": branch_type_counts
        }

    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/getpos", response_model=PosAPIResponse)
async def get_pos(
    AccessToken: Optional[str] = Query(None, description="Access token (not validated)"),
    BranchCode: Optional[str] = Query(None, description="Branch code filter"),
    AgencyCode: Optional[str] = Query(None, description="Agency code filter"),
    SubAgencyCode: Optional[str] = Query(None, description="Sub agency code filter"),
    UpdatedDate: Optional[str] = Query(None, description="Updated date in YYYYMMDD format")
):
    """
    Get POS information based on filters and updated date

    - **AccessToken**: Token (not validated as per requirement)
    - **BranchCode**: Branch code filter
    - **AgencyCode**: Agency code filter
    - **SubAgencyCode**: Sub agency code filter
    - **UpdatedDate**: Date filter in YYYYMMDD format for mapping logic
    """
    try:
        logger.info(f"Getting POS - Branch: {BranchCode}, Agency: {AgencyCode}, SubAgency: {SubAgencyCode}, Date: {UpdatedDate}")

        # Get POS from database
        pos_data = db_manager.get_pos(
            branch_code=BranchCode,
            agency_code=AgencyCode,
            sub_agency_code=SubAgencyCode,
            updated_date=UpdatedDate
        )

        # Convert to PosItem models
        pos_items = [
            PosItem(
                Id=pos['Id'],
                PosCode=pos['PosCode'],
                PosName=pos['PosName'],
                PosStatus=pos['PosStatus'],
                PosType=pos['PosType'],
                AgencyCode=pos['AgencyCode'],
                SubAgencyCode=pos['SubAgencyCode'],
                AreaCode=pos['AreaCode'],
                ProvinceCode=pos['ProvinceCode'],
                DistrictCode=pos['DistrictCode'],
                WardCode=pos['WardCode'],
                Address=pos['Address'],
                Status=pos['Status'],
                PosNumber=pos['PosNumber'],
                Certificate=pos['Certificate'],
                ContractNumber=pos['ContractNumber'],
                ContractDate=pos['ContractDate'],
                UpdateContractNumber=pos['UpdateContractNumber'],
                UpdateContractDate=pos['UpdateContractDate'],
                ReleaseContractNumber=pos['ReleaseContractNumber'],
                ReleaseContractDate=pos['ReleaseContractDate'],
                TerminalCount=pos['TerminalCount'],
                BranchCode=pos['BranchCode']
            )
            for pos in pos_data
        ]

        logger.info(f"Returning {len(pos_items)} POS records")

        return PosAPIResponse(
            ErrorCode=SUCCESS_CODE,
            ErrorMsg=SUCCESS_MSG,
            Data=pos_items
        )

    except Exception as e:
        logger.error(f"Error in get_pos: {e}")
        return PosAPIResponse(
            ErrorCode=ERROR_CODE,
            ErrorMsg=f"Error: {str(e)}",
            Data=[]
        )

@app.get("/api/merger-info")
async def get_merger_info():
    """Get province merger information"""
    try:
        from province_merger import province_merger
        return province_merger.get_merger_info()
    except Exception as e:
        logger.error(f"Error getting merger info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/pos-info")
async def get_pos_info():
    """Get POS mapping information"""
    try:
        from pos_mapper import pos_mapper
        return pos_mapper.get_mapping_info()
    except Exception as e:
        logger.error(f"Error getting POS info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/branch-info")
async def get_branch_info():
    """Get branch mapping information"""
    try:
        from branch_mapper import branch_mapper
        return branch_mapper.get_mapping_info()
    except Exception as e:
        logger.error(f"Error getting branch info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/branches")
async def get_branches():
    """Get all branches"""
    try:
        from branch_mapper import branch_mapper
        return {
            "ErrorCode": SUCCESS_CODE,
            "ErrorMsg": SUCCESS_MSG,
            "Data": branch_mapper.get_all_branches()
        }
    except Exception as e:
        logger.error(f"Error getting branches: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ward-info")
async def get_ward_info():
    """Get ward mapping information"""
    try:
        from ward_mapper import ward_mapper
        mapping_info = ward_mapper.get_mapping_info()
        smart_mapping_stats = ward_mapper.get_mapping_statistics()

        # Combine both info
        return {
            **mapping_info,
            "smart_mapping": smart_mapping_stats
        }
    except Exception as e:
        logger.error(f"Error getting ward info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/reimport")
async def reimport_data():
    """Manually trigger data reimport"""
    try:
        logger.info("Manual data reimport triggered")
        success = db_manager.import_locations_data()

        if success:
            count = db_manager.get_locations_count()
            return {
                "success": True,
                "message": f"Data reimported successfully. Total records: {count}"
            }
        else:
            return {
                "success": False,
                "message": "Data reimport failed"
            }

    except Exception as e:
        logger.error(f"Error in reimport: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    from config import API_HOST, API_PORT, DEBUG
    
    uvicorn.run(
        "main:app",
        host=API_HOST,
        port=API_PORT,
        reload=DEBUG
    )
