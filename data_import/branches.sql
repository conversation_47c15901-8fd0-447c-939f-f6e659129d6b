/*
 Navicat Premium Data Transfer

 Source Server         : DMS_DEV2
 Source Server Type    : PostgreSQL
 Source Server Version : 160009 (160009)
 Source Host           : **************:5000
 Source Catalog        : dms-dev
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 160009 (160009)
 File Encoding         : 65001

 Date: 01/07/2025 13:29:01
*/


-- ----------------------------
-- Table structure for branches
-- ----------------------------
DROP TABLE IF EXISTS "public"."branches";
CREATE TABLE "public"."branches" (
  "id" varchar(191) COLLATE "pg_catalog"."default" NOT NULL,
  "code" varchar(191) COLLATE "pg_catalog"."default",
  "code2" varchar(191) COLLATE "pg_catalog"."default",
  "name" varchar(191) COLLATE "pg_catalog"."default" NOT NULL,
  "address" varchar(191) COLLATE "pg_catalog"."default",
  "phone" varchar(191) COLLATE "pg_catalog"."default",
  "email" varchar(191) COLLATE "pg_catalog"."default",
  "created_at" timestamp(0),
  "updated_at" timestamp(0),
  "branch_type" int4 NOT NULL DEFAULT 1
)
;
ALTER TABLE "public"."branches" OWNER TO "dmsprod";

-- ----------------------------
-- Records of branches
-- ----------------------------
BEGIN;
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('0CB431587E8135BA1A907E4ED8000D24', 'HP', 'CNHP', 'Chi nhánh Hải Phòng', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('6E46C3A00A0315F10C5579B150433137', 'CTO', 'CNCT', 'Chi nhánh Cần Thơ', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('733B8AA3159951BD8FB10A6E874B2329', 'KH', 'CNKH', 'Chi nhánh Khánh Hòa', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('82DAE59E230603347167263F2A9A1030', 'VT', 'CNBRVT', 'Chi nhánh BR - VT', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('AA80211881A7A6C41F28E8B184A31D86', 'HCM', 'CN HCM', 'Chi nhánh TP. HCM', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('FBE147A25BDF5FDA9AED5E96600A80F2', 'NA', 'CNNA', 'Chi nhánh Nghệ An', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 1);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('3983b80f937d4aa0aa35d74dc9a8283f', 'SMS', 'SMS', 'Kênh điện thoại', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 2);
INSERT INTO "public"."branches" ("id", "code", "code2", "name", "address", "phone", "email", "created_at", "updated_at", "branch_type") VALUES ('0789c05a5f9c4f6a8d051e38ede0ce64', 'TOHO', 'TOHO', 'Hội sở', NULL, NULL, NULL, '2024-06-14 16:15:30', '2024-06-14 16:15:30', 0);
COMMIT;

-- ----------------------------
-- Uniques structure for table branches
-- ----------------------------
ALTER TABLE "public"."branches" ADD CONSTRAINT "branches_code_unique" UNIQUE ("code");

-- ----------------------------
-- Primary Key structure for table branches
-- ----------------------------
ALTER TABLE "public"."branches" ADD CONSTRAINT "branches_pkey" PRIMARY KEY ("id");
