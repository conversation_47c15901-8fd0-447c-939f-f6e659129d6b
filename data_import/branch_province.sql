/*
 Navicat Premium Data Transfer

 Source Server         : DMS_DEV2
 Source Server Type    : PostgreSQL
 Source Server Version : 160009 (160009)
 Source Host           : **************:5000
 Source Catalog        : dms-dev
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 160009 (160009)
 File Encoding         : 65001

 Date: 01/07/2025 13:29:08
*/


-- ----------------------------
-- Table structure for branch_province
-- ----------------------------
DROP TABLE IF EXISTS "public"."branch_province";
CREATE TABLE "public"."branch_province" (
  "id" int8 NOT NULL DEFAULT nextval('branch_province_id_seq'::regclass),
  "branch_code" varchar(191) COLLATE "pg_catalog"."default" NOT NULL,
  "location_code" varchar(191) COLLATE "pg_catalog"."default" NOT NULL,
  "min_visit" int4,
  "created_at" timestamp(0),
  "updated_at" timestamp(0)
)
;
ALTER TABLE "public"."branch_province" OWNER TO "dmsprod";
COMMENT ON COLUMN "public"."branch_province"."location_code" IS 'Thị trường tương ứng với province';
COMMENT ON COLUMN "public"."branch_province"."min_visit" IS 'Số lần ghé thăm tối thiểu';

-- ----------------------------
-- Records of branch_province
-- ----------------------------
BEGIN;
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (1, 'HP', 'LAOCAI', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (2, 'HP', 'LC', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (3, 'HP', 'HN', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (4, 'HP', 'PHUTHO', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (5, 'HP', 'CAOBANG', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (6, 'HP', 'THAINGUYEN', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (7, 'HP', 'YENBAI', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (8, 'HP', 'HG', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (9, 'HP', 'QN', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (10, 'HP', 'HAIDUONG', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (12, 'HP', 'HP', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (13, 'HP', 'SL', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (14, 'HP', 'BN', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (15, 'HP', 'LANGSON', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (16, 'HP', 'BACGIANG', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (17, 'HP', 'DB', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (18, 'HP', 'BK', NULL, '2024-06-24 11:14:40', '2024-06-24 11:14:40');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (19, 'CTO', 'KIENGIANG', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (20, 'CTO', 'VINHLONG', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (21, 'CTO', 'AG', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (22, 'CTO', 'CTH', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (23, 'CTO', 'CAMAU', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (24, 'CTO', 'DT', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (25, 'CTO', 'BL', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (26, 'CTO', 'HAUGIANG', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (27, 'CTO', 'TRAVINH', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (28, 'CTO', 'BENTRE', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (29, 'CTO', 'SOCTRANG', NULL, '2024-06-24 11:15:48', '2024-06-24 11:15:48');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (30, 'KH', 'BINHDINH', NULL, '2024-06-24 11:17:19', '2024-06-24 11:17:19');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (31, 'KH', 'QUANGNGAI', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (32, 'KH', 'KONTUM', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (33, 'KH', 'NINHTHUAN', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (34, 'KH', 'PHUYEN', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (35, 'KH', 'DAN', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (36, 'KH', 'KH', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (37, 'KH', 'QUANGNAM', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (38, 'KH', 'DL', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (39, 'KH', 'GIALAI', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (40, 'KH', 'ÐKN', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (41, 'KH', 'LAMDONG', NULL, '2024-06-24 11:17:20', '2024-06-24 11:17:20');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (42, 'VT', 'BINHPHUOC', NULL, '2024-06-24 11:18:47', '2024-06-24 11:18:47');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (43, 'VT', 'TAYNINH', NULL, '2024-06-24 11:18:47', '2024-06-24 11:18:47');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (46, 'VT', 'DN', NULL, '2024-06-24 11:18:47', '2024-06-24 11:18:47');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (49, 'VT', 'LONGAN', NULL, '2024-06-24 11:18:47', '2024-06-24 11:18:47');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (50, 'HCM', 'HCM', NULL, '2024-06-24 11:19:03', '2024-06-24 11:19:03');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (51, 'NA', 'NGHEAN', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (52, 'NA', 'HUE', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (53, 'NA', 'HANAM', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (54, 'NA', 'QUANGBINH', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (55, 'NA', 'QUANGTRI', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (57, 'NA', 'NINHBINH', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (60, 'NA', 'THANHHOA', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (61, 'NA', 'NAMDINH', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (62, 'NA', 'HATINH', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (63, 'NA', 'THAIBINH', NULL, '2025-01-17 17:07:04', '2025-01-17 17:07:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (84, 'HP', 'VINHPHUC', NULL, '2025-04-24 16:58:09', '2025-04-24 16:58:09');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (86, 'HCM', 'BR', NULL, '2025-07-01 11:25:55', '2025-07-01 11:25:55');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (87, 'HCM', 'BD', NULL, '2025-07-01 11:26:04', '2025-07-01 11:26:04');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (88, 'CTO', 'TIENGIANG', NULL, '2025-07-01 11:27:24', '2025-07-01 11:27:24');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (89, 'HP', 'TQ', NULL, '2025-07-01 11:29:12', '2025-07-01 11:29:12');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (90, 'HP', 'HOABINH', NULL, '2025-07-01 11:29:25', '2025-07-01 11:29:25');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (91, 'NA', 'HUNGYEN', NULL, '2025-07-01 11:31:17', '2025-07-01 11:31:17');
INSERT INTO "public"."branch_province" ("id", "branch_code", "location_code", "min_visit", "created_at", "updated_at") VALUES (92, 'KH', 'BINHTHUAN', NULL, '2025-07-01 11:32:18', '2025-07-01 11:32:18');
COMMIT;

-- ----------------------------
-- Primary Key structure for table branch_province
-- ----------------------------
ALTER TABLE "public"."branch_province" ADD CONSTRAINT "branch_province_pkey" PRIMARY KEY ("id");
