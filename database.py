"""
Database management and data import functionality
"""
import sqlite3
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from config import DATABASE_PATH, DATA_IMPORT_FOLDER
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialize SQLite database and create tables"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Create locations table (adapted from PostgreSQL to SQLite)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS locations (
                    id TEXT PRIMARY KEY,
                    locationType INTEGER NOT NULL DEFAULT 0,
                    locationCode TEXT,
                    locationCode2 TEXT,
                    locationName TEXT,
                    locationDigitCode TEXT,
                    parentCode TEXT,
                    parentCode2 TEXT,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ''')

            # Create pos table (adapted from PostgreSQL to SQLite)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pos (
                    id TEXT PRIMARY KEY,
                    posCode TEXT NOT NULL,
                    posStatus INTEGER NOT NULL DEFAULT 0,
                    posName TEXT,
                    posType TEXT,
                    pos_type_id INTEGER,
                    agencyCode TEXT,
                    subAgencyCode TEXT,
                    areaCode TEXT,
                    provinceCode TEXT,
                    provinceCode2 TEXT,
                    districtCode TEXT,
                    districtCode2 TEXT,
                    wardCode TEXT,
                    address TEXT,
                    posNumber TEXT,
                    certificate TEXT,
                    contractNumber TEXT,
                    contractDate TEXT,
                    updateContractNumber TEXT,
                    updateContractDate TEXT,
                    releaseContractNumber TEXT,
                    releaseContractDate TEXT,
                    terminalCount TEXT,
                    branchCode TEXT,
                    branchCode2 TEXT,
                    lat REAL,
                    lon REAL,
                    staff_id INTEGER,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ''')

            # Create branches table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS branches (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE,
                    code2 TEXT,
                    name TEXT NOT NULL,
                    address TEXT,
                    phone TEXT,
                    email TEXT,
                    branch_type INTEGER,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ''')

            # Create branch_province table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS branch_province (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    branch_code TEXT NOT NULL,
                    location_code TEXT NOT NULL,
                    min_visit INTEGER,
                    created_at TIMESTAMP,
                    updated_at TIMESTAMP
                )
            ''')

            # Create pos_new table for comparison
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pos_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pos_code TEXT NOT NULL,
                    province_name TEXT,
                    branch_name TEXT,
                    pos_name TEXT,
                    created_at TIMESTAMP
                )
            ''')

            # Create ward_new table for new ward mapping
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ward_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    province_name TEXT NOT NULL,
                    ward_name TEXT NOT NULL,
                    old_wards TEXT,
                    province_code TEXT,
                    ward_code TEXT,
                    created_at TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def import_locations_data(self):
        """Import data from locations.sql file"""
        try:
            sql_file_path = os.path.join(DATA_IMPORT_FOLDER, "locations.sql")
            
            if not os.path.exists(sql_file_path):
                logger.error(f"SQL file not found: {sql_file_path}")
                return False
            
            # Read SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # Extract INSERT statements
            insert_pattern = r"INSERT INTO.*?locations.*?VALUES\s*\((.*?)\);"
            matches = re.findall(insert_pattern, sql_content, re.DOTALL | re.IGNORECASE)
            
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Clear existing data
            cursor.execute("DELETE FROM locations")
            
            # Process each INSERT statement
            for match in matches:
                # Clean and parse values
                values_str = match.strip()
                # Simple parsing - split by comma and clean quotes
                values = self._parse_insert_values(values_str)
                
                if len(values) >= 10:  # Ensure we have all required fields
                    cursor.execute('''
                        INSERT INTO locations 
                        (id, locationType, locationCode, locationCode2, locationName, 
                         locationDigitCode, parentCode, parentCode2, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', values[:10])
            
            conn.commit()
            conn.close()
            
            # Log import statistics
            count = self.get_locations_count()
            logger.info(f"Successfully imported {count} location records")
            return True
            
        except Exception as e:
            logger.error(f"Error importing locations data: {e}")
            return False

    def import_pos_data(self):
        """Import data from pos.sql file"""
        try:
            sql_file_path = os.path.join(DATA_IMPORT_FOLDER, "pos.sql")

            if not os.path.exists(sql_file_path):
                logger.error(f"POS SQL file not found: {sql_file_path}")
                return False

            # Read SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()

            # Extract INSERT statements for pos table
            insert_pattern = r"INSERT INTO.*?pos.*?VALUES\s*\((.*?)\);"
            matches = re.findall(insert_pattern, sql_content, re.DOTALL | re.IGNORECASE)

            conn = self.get_connection()
            cursor = conn.cursor()

            # Clear existing data
            cursor.execute("DELETE FROM pos")

            # Process each INSERT statement
            imported_count = 0
            for match in matches:
                # Clean and parse values
                values_str = match.strip()
                values = self._parse_insert_values(values_str)

                if len(values) >= 25:  # Ensure we have minimum required fields
                    # Map values to pos table columns
                    cursor.execute('''
                        INSERT INTO pos
                        (id, posCode, posStatus, posName, posType, pos_type_id,
                         agencyCode, subAgencyCode, areaCode, provinceCode, provinceCode2,
                         districtCode, districtCode2, wardCode, address, posNumber,
                         certificate, contractNumber, updateContractNumber, releaseContractNumber,
                         terminalCount, branchCode, branchCode2, lat, lon, staff_id,
                         created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', values[:28])  # Take first 28 values
                    imported_count += 1

            conn.commit()
            conn.close()

            logger.info(f"Successfully imported {imported_count} POS records")
            return True

        except Exception as e:
            logger.error(f"Error importing POS data: {e}")
            return False

    def import_branches_data(self):
        """Import data from branches.sql file"""
        try:
            sql_file_path = os.path.join(DATA_IMPORT_FOLDER, "branches.sql")

            if not os.path.exists(sql_file_path):
                logger.error(f"Branches SQL file not found: {sql_file_path}")
                return False

            # Read SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()

            # Extract INSERT statements for branches table
            insert_pattern = r"INSERT INTO.*?branches.*?VALUES\s*\((.*?)\);"
            matches = re.findall(insert_pattern, sql_content, re.DOTALL | re.IGNORECASE)

            conn = self.get_connection()
            cursor = conn.cursor()

            # Clear existing data
            cursor.execute("DELETE FROM branches")

            # Process each INSERT statement
            imported_count = 0
            for match in matches:
                # Clean and parse values
                values_str = match.strip()
                values = self._parse_insert_values(values_str)

                if len(values) >= 8:  # Ensure we have minimum required fields
                    # Map values to branches table columns
                    cursor.execute('''
                        INSERT INTO branches
                        (id, code, code2, name, address, phone, email, created_at, updated_at, branch_type)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', values[:10])
                    imported_count += 1

            conn.commit()
            conn.close()

            logger.info(f"Successfully imported {imported_count} branches records")
            return True

        except Exception as e:
            logger.error(f"Error importing branches data: {e}")
            return False

    def import_branch_province_data(self):
        """Import data from branch_province.sql file"""
        try:
            sql_file_path = os.path.join(DATA_IMPORT_FOLDER, "branch_province.sql")

            if not os.path.exists(sql_file_path):
                logger.error(f"Branch province SQL file not found: {sql_file_path}")
                return False

            # Read SQL file
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()

            # Extract INSERT statements for branch_province table
            insert_pattern = r"INSERT INTO.*?branch_province.*?VALUES\s*\((.*?)\);"
            matches = re.findall(insert_pattern, sql_content, re.DOTALL | re.IGNORECASE)

            conn = self.get_connection()
            cursor = conn.cursor()

            # Clear existing data
            cursor.execute("DELETE FROM branch_province")

            # Process each INSERT statement
            imported_count = 0
            for match in matches:
                # Clean and parse values
                values_str = match.strip()
                values = self._parse_insert_values(values_str)

                if len(values) >= 5:  # Ensure we have minimum required fields
                    # Map values to branch_province table columns (skip auto-increment id)
                    cursor.execute('''
                        INSERT INTO branch_province
                        (branch_code, location_code, min_visit, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', values[1:6])  # Skip the first value (id)
                    imported_count += 1

            conn.commit()
            conn.close()

            logger.info(f"Successfully imported {imported_count} branch-province mappings")
            return True

        except Exception as e:
            logger.error(f"Error importing branch-province data: {e}")
            return False

    def import_pos_new_data(self):
        """Import data from pos_new.csv file"""
        try:
            csv_file_path = os.path.join(DATA_IMPORT_FOLDER, "pos_new.csv")

            if not os.path.exists(csv_file_path):
                logger.error(f"POS new CSV file not found: {csv_file_path}")
                return False

            # Read CSV file
            import csv
            from datetime import datetime

            conn = self.get_connection()
            cursor = conn.cursor()

            # Clear existing data
            cursor.execute("DELETE FROM pos_new")

            # Process CSV file
            imported_count = 0
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.reader(file, delimiter=';')
                next(reader)  # Skip header

                for row in reader:
                    if len(row) >= 4:
                        pos_code = row[0].strip()
                        province_name = row[1].strip()
                        branch_name = row[2].strip()
                        pos_name = row[3].strip()

                        if pos_code and province_name and branch_name and pos_name:
                            cursor.execute('''
                                INSERT INTO pos_new
                                (pos_code, province_name, branch_name, pos_name, created_at)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (pos_code, province_name, branch_name, pos_name, datetime.now()))
                            imported_count += 1

            conn.commit()
            conn.close()

            logger.info(f"Successfully imported {imported_count} pos_new records")
            return True

        except Exception as e:
            logger.error(f"Error importing pos_new data: {e}")
            return False

    def import_ward_new_data(self):
        """Import data from new_ward.json file"""
        try:
            json_file_path = os.path.join(DATA_IMPORT_FOLDER, "new_ward.json")

            if not os.path.exists(json_file_path):
                logger.error(f"Ward new JSON file not found: {json_file_path}")
                return False

            # Read JSON file
            import json
            from datetime import datetime

            with open(json_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            conn = self.get_connection()
            cursor = conn.cursor()

            # Clear existing data
            cursor.execute("DELETE FROM ward_new")

            # Process JSON data
            imported_count = 0
            for record in data:
                if 'columns' in record and len(record['columns']) >= 3:
                    columns = record['columns']
                    province_name = columns[0].strip()
                    ward_name = columns[1].strip()
                    old_wards = columns[2].strip()

                    # Skip header row
                    if province_name == 'Tỉnh':
                        continue

                    # Handle "Không sáp nhập" - these are new wards that don't merge old ones
                    if old_wards == 'Không sáp nhập':
                        old_wards = 'Phường/xã mới'  # Mark as new ward

                    if province_name and ward_name and old_wards:
                        # Find province code
                        province_code = self._find_province_code_by_name(province_name)

                        # Generate ward code (using province code + sequential number)
                        ward_code = f"{province_code}W{imported_count:04d}" if province_code else f"W{imported_count:04d}"

                        cursor.execute('''
                            INSERT INTO ward_new
                            (province_name, ward_name, old_wards, province_code, ward_code, created_at)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (province_name, ward_name, old_wards, province_code, ward_code, datetime.now()))
                        imported_count += 1

            conn.commit()
            conn.close()

            logger.info(f"Successfully imported {imported_count} ward_new records")
            return True

        except Exception as e:
            logger.error(f"Error importing ward_new data: {e}")
            return False

    def _find_province_code_by_name(self, province_name: str) -> str:
        """Find province code by name"""
        try:
            # Normalize province name for matching
            normalized_name = province_name.lower().strip()

            # Common mappings
            name_mappings = {
                'an giang': 'AG',
                'bà rịa - vũng tàu': 'BR',
                'bạc liêu': 'BL',
                'bắc giang': 'BACGIANG',
                'bắc kạn': 'BK',
                'bắc ninh': 'BN',
                'bến tre': 'BENTRE',
                'bình định': 'BINHDINH',
                'bình dương': 'BD',
                'bình phước': 'BINHPHUOC',
                'bình thuận': 'BINHTHUAN',
                'cà mau': 'CAMAU',
                'cần thơ': 'CTH',
                'cao bằng': 'CAOBANG',
                'đà nẵng': 'DAN',
                'đắk lắk': 'DL',
                'đắk nông': 'ÐKN',
                'điện biên': 'DB',
                'đồng nai': 'DN',
                'đồng tháp': 'DT',
                'gia lai': 'GIALAI',
                'hà giang': 'HG',
                'hà nam': 'HANAM',
                'hà nội': 'HN',
                'hà tĩnh': 'HATINH',
                'hải dương': 'HAIDUONG',
                'hải phòng': 'HP',
                'hậu giang': 'HAUGIANG',
                'hòa bình': 'HOABINH',
                'hưng yên': 'HUNGYEN',
                'khánh hòa': 'KH',
                'kiên giang': 'KIENGIANG',
                'kon tum': 'KONTUM',
                'lai châu': 'LC',
                'lâm đồng': 'LAMDONG',
                'lạng sơn': 'LANGSON',
                'lào cai': 'LAOCAI',
                'long an': 'LONGAN',
                'nam định': 'NAMDINH',
                'nghệ an': 'NGHEAN',
                'ninh bình': 'NINHBINH',
                'ninh thuận': 'NINHTHUAN',
                'phú thọ': 'PHUTHO',
                'phú yên': 'PHUYEN',
                'quảng bình': 'QUANGBINH',
                'quảng nam': 'QUANGNAM',
                'quảng ngãi': 'QUANGNGAI',
                'quảng ninh': 'QN',
                'quảng trị': 'QUANGTRI',
                'sóc trăng': 'SOCTRANG',
                'sơn la': 'SL',
                'tây ninh': 'TAYNINH',
                'thái bình': 'THAIBINH',
                'thái nguyên': 'THAINGUYEN',
                'thanh hóa': 'THANHHOA',
                'thừa thiên huế': 'HUE',
                'huế': 'HUE',
                'tiền giang': 'TIENGIANG',
                'tp. hồ chí minh': 'HCM',
                'tp hcm': 'HCM',
                'hồ chí minh': 'HCM',
                'trà vinh': 'TRAVINH',
                'tuyên quang': 'TQ',
                'vĩnh long': 'VINHLONG',
                'vĩnh phúc': 'VINHPHUC',
                'yên bái': 'YENBAI'
            }

            return name_mappings.get(normalized_name, 'UNKNOWN')

        except Exception as e:
            logger.error(f"Error finding province code for {province_name}: {e}")
            return 'UNKNOWN'
    
    def _parse_insert_values(self, values_str: str) -> List[str]:
        """Parse INSERT VALUES string into list of values"""
        # Simple parser for SQL values
        values = []
        current_value = ""
        in_quotes = False
        quote_char = None
        
        i = 0
        while i < len(values_str):
            char = values_str[i]
            
            if not in_quotes:
                if char in ["'", '"']:
                    in_quotes = True
                    quote_char = char
                elif char == ',':
                    values.append(current_value.strip())
                    current_value = ""
                    i += 1
                    continue
                else:
                    current_value += char
            else:
                if char == quote_char:
                    # Check if it's escaped quote
                    if i + 1 < len(values_str) and values_str[i + 1] == quote_char:
                        current_value += char
                        i += 1  # Skip next quote
                    else:
                        in_quotes = False
                        quote_char = None
                else:
                    current_value += char
            
            i += 1
        
        # Add last value
        if current_value.strip():
            values.append(current_value.strip())
        
        # Clean values (remove quotes, handle NULL)
        cleaned_values = []
        for value in values:
            value = value.strip()
            if value.upper() == 'NULL' or value == '':
                cleaned_values.append('')
            elif value.startswith("'") and value.endswith("'"):
                cleaned_values.append(value[1:-1])
            elif value.startswith('"') and value.endswith('"'):
                cleaned_values.append(value[1:-1])
            else:
                cleaned_values.append(value)
        
        return cleaned_values
    
    def get_locations_count(self) -> int:
        """Get total count of locations"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM locations")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting locations count: {e}")
            return 0
    
    def get_locations(self, location_type: Optional[str] = None,
                     updated_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get locations with optional filtering and province merger logic"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Build query - now include merged_location_code
            query = """
                SELECT id, locationType, locationCode, locationDigitCode, locationName,
                       parentCode, merged_location_code
                FROM locations
            """
            params = []
            conditions = []

            # Filter by location type
            if location_type is not None:
                conditions.append("locationType = ?")
                params.append(int(location_type))

            # Note: UpdatedDate is now used only for merger logic, not database filtering
            # We removed the database date filtering to allow merger logic to work properly

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            # Import mappers here to avoid circular import
            from province_merger import province_merger
            from ward_mapper import ward_mapper

            # Convert to list of dictionaries with merger and ward logic
            locations = []

            # Determine if we should use ward mapping
            use_ward_mapping = ward_mapper.should_use_ward_mapping(updated_date)

            # Handle specific LocationType requests
            if location_type is not None:
                location_type_int = int(location_type)

                # Handle LocationType 2 (Districts) - return wards if using ward mapping
                if location_type_int == 2:
                    if use_ward_mapping:
                        # Return wards instead of districts - get all provinces first
                        temp_conn = self.get_connection()
                        temp_cursor = temp_conn.cursor()
                        temp_cursor.execute('SELECT locationCode FROM locations WHERE locationType = 1')
                        all_provinces = temp_cursor.fetchall()
                        temp_conn.close()

                        for (province_code,) in all_provinces:
                            # Apply province merger logic
                            if not province_merger.should_show_location(province_code, updated_date):
                                continue  # Skip merged provinces

                            # Get wards for this province
                            wards = ward_mapper.get_wards_for_province(province_code, updated_date)
                            for ward in wards:
                                ward_location = ward_mapper.convert_ward_to_location_format(ward, 2)
                                locations.append(ward_location)

                        return locations
                    # else: Continue with normal district processing below

                # Handle LocationType 3 (Wards) - don't return anything if using ward mapping
                elif location_type_int == 3:
                    if use_ward_mapping:
                        return []  # No wards returned when using ward mapping (wards replace districts)
                    # else: Continue with normal ward processing below

            # Handle case when no LocationType specified - need to return all types
            elif location_type is None and use_ward_mapping:
                # Add wards as LocationType 2 when no specific type requested
                temp_conn = self.get_connection()
                temp_cursor = temp_conn.cursor()
                temp_cursor.execute('SELECT locationCode FROM locations WHERE locationType = 1')
                all_provinces = temp_cursor.fetchall()
                temp_conn.close()

                for (province_code,) in all_provinces:
                    # Apply province merger logic
                    if not province_merger.should_show_location(province_code, updated_date):
                        continue  # Skip merged provinces

                    # Get wards for this province
                    wards = ward_mapper.get_wards_for_province(province_code, updated_date)
                    for ward in wards:
                        ward_location = ward_mapper.convert_ward_to_location_format(ward, 2)
                        locations.append(ward_location)

            # Normal processing for other location types
            for row in rows:
                location_id, location_type_val, location_code, location_digit_code, location_name, parent_code, merged_location_code = row

                # Always include regions (LocationType 0)
                if location_type_val == 0:  # Region
                    locations.append({
                        'Id': location_id,
                        'LocationType': location_type_val,
                        'LocationCode': location_code or '',
                        'LocationDigitCode': location_digit_code or '',
                        'LocationName': location_name or '',
                        'ParentCode': parent_code or '',
                        'Status': 'new'
                    })

                # Apply province merger logic
                elif location_type_val == 1:  # Province
                    # Check if this province should be shown based on merger date
                    if not province_merger.should_show_location(location_code, updated_date):
                        continue  # Skip merged provinces for post-merger dates

                    # Use merged code for post-merger dates
                    display_code = province_merger.get_merged_location_code(location_code, updated_date)

                    # If this is a merged province, update the name to reflect the target province
                    if display_code != location_code and updated_date and updated_date >= province_merger.merger_date:
                        # Get the target province name
                        temp_conn = self.get_connection()
                        temp_cursor = temp_conn.cursor()
                        temp_cursor.execute(
                            "SELECT locationName FROM locations WHERE locationCode = ? AND locationType = 1",
                            (display_code,)
                        )
                        target_name = temp_cursor.fetchone()
                        if target_name:
                            location_name = target_name[0]
                        temp_conn.close()

                    locations.append({
                        'Id': location_id,
                        'LocationType': location_type_val,
                        'LocationCode': display_code,
                        'LocationDigitCode': location_digit_code or '',
                        'LocationName': location_name or '',
                        'ParentCode': parent_code or '',
                        'Status': 'new'
                    })

                elif location_type_val == 2:  # District
                    # Skip districts if using ward mapping AND no specific LocationType requested
                    # OR if LocationType=2 was specifically requested and we're using ward mapping (already handled above)
                    if use_ward_mapping and (location_type is None or int(location_type) == 2):
                        continue

                    # For districts, update parent code based on merger
                    display_parent_code = parent_code
                    if parent_code:
                        display_parent_code = province_merger.get_merged_location_code(parent_code, updated_date)

                    locations.append({
                        'Id': location_id,
                        'LocationType': location_type_val,
                        'LocationCode': location_code or '',
                        'LocationDigitCode': location_digit_code or '',
                        'LocationName': location_name or '',
                        'ParentCode': display_parent_code or '',
                        'Status': 'new'
                    })

                else:  # Ward (LocationType 3)
                    # Skip wards if using ward mapping
                    if use_ward_mapping:
                        continue

                    # For wards, update parent code based on merger
                    display_parent_code = parent_code
                    if parent_code:
                        display_parent_code = province_merger.get_merged_location_code(parent_code, updated_date)

                    locations.append({
                        'Id': location_id,
                        'LocationType': location_type_val,
                        'LocationCode': location_code or '',
                        'LocationDigitCode': location_digit_code or '',
                        'LocationName': location_name or '',
                        'ParentCode': display_parent_code or '',
                        'Status': 'new'
                    })

            return locations

        except Exception as e:
            logger.error(f"Error getting locations: {e}")
            return []

    def get_pos_count(self) -> int:
        """Get total count of POS"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM pos")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting POS count: {e}")
            return 0

    def get_branches_count(self) -> int:
        """Get total count of branches"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM branches")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting branches count: {e}")
            return 0

    def get_branch_province_count(self) -> int:
        """Get total count of branch-province mappings"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM branch_province")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting branch-province count: {e}")
            return 0

    def get_pos_new_count(self) -> int:
        """Get total count of pos_new"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM pos_new")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting pos_new count: {e}")
            return 0

    def get_ward_new_count(self) -> int:
        """Get total count of ward_new"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM ward_new")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            logger.error(f"Error getting ward_new count: {e}")
            return 0

    def get_pos(self, branch_code: Optional[str] = None,
                agency_code: Optional[str] = None,
                sub_agency_code: Optional[str] = None,
                updated_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get POS with optional filtering and mapping logic"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Build query - include mapped_pos_code if exists
            query = """
                SELECT id, posCode, posName, posStatus, posType, agencyCode,
                       subAgencyCode, areaCode, provinceCode, districtCode, wardCode,
                       address, posNumber, certificate, contractNumber, contractDate,
                       updateContractNumber, updateContractDate, releaseContractNumber,
                       releaseContractDate, terminalCount, branchCode
                FROM pos
            """
            params = []
            conditions = []

            # Filter by branch code
            if branch_code:
                conditions.append("branchCode = ?")
                params.append(branch_code)

            # Filter by agency code
            if agency_code:
                conditions.append("agencyCode = ?")
                params.append(agency_code)

            # Filter by sub agency code
            if sub_agency_code:
                conditions.append("subAgencyCode = ?")
                params.append(sub_agency_code)

            # Note: UpdatedDate is used only for mapping logic, not database filtering

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()

            # Import pos_mapper here to avoid circular import
            from pos_mapper import pos_mapper

            # Convert to list of dictionaries with mapping logic
            pos_list = []
            for row in rows:
                (pos_id, pos_code, pos_name, pos_status, pos_type, agency_code_val,
                 sub_agency_code_val, area_code, province_code, district_code, ward_code,
                 address, pos_number, certificate, contract_number, contract_date,
                 update_contract_number, update_contract_date, release_contract_number,
                 release_contract_date, terminal_count, branch_code_val) = row

                # Apply mapping logic for ALL POS regardless of status
                # Get mapped info - only apply if POS has mapping
                mapped_info = pos_mapper.get_mapped_pos_info(pos_code, updated_date)

                # For after mapping date, use mapped info if available, otherwise keep original
                if updated_date and updated_date >= pos_mapper.mapping_date and mapped_info:
                    display_pos_name = mapped_info.get('pos_name', pos_name)
                    display_province_code = mapped_info.get('province_code', province_code)
                else:
                    # Before mapping date or no mapping available - use original data
                    display_pos_name = pos_name
                    display_province_code = province_code if province_code and province_code.strip() else None

                # IMPORTANT: Apply province merger for ALL POS after mapping date
                if updated_date and updated_date >= "20250701" and display_province_code:
                    from province_merger import province_merger
                    merged_province_code = province_merger.get_merged_location_code(display_province_code, updated_date)
                    display_province_code = merged_province_code

                # Get branch code based on province mapping (for ALL POS with province info)
                from branch_mapper import branch_mapper
                from ward_mapper import ward_mapper

                if display_province_code:
                    mapped_branch_code = branch_mapper.get_branch_for_province(display_province_code, updated_date, address)
                    display_branch_code = mapped_branch_code if mapped_branch_code else branch_code_val

                    # Apply intelligent ward mapping to district/ward codes
                    if ward_mapper.should_use_ward_mapping(updated_date):
                        # Create temporary POS record for smart mapping
                        temp_pos_record = {
                            'PosCode': pos_code,
                            'ProvinceCode': display_province_code,
                            'Address': address,
                            'DistrictCode': district_code,
                            'WardCode': ward_code
                        }

                        # Apply smart ward mapping
                        mapped_pos = ward_mapper.smart_map_pos_to_ward(temp_pos_record, updated_date)
                        display_district_code = mapped_pos.get('DistrictCode', district_code)
                        display_ward_code = mapped_pos.get('WardCode', ward_code)
                    else:
                        display_district_code = district_code
                        display_ward_code = ward_code
                else:
                    # No province code - no branch or ward mapping
                    display_branch_code = None
                    display_district_code = district_code
                    display_ward_code = ward_code



                pos_list.append({
                    'Id': pos_id,
                    'PosCode': pos_code,
                    'PosName': display_pos_name or '',
                    'PosStatus': pos_status,
                    'PosType': pos_type or '',
                    'AgencyCode': agency_code_val or '',
                    'SubAgencyCode': sub_agency_code_val or '',
                    'AreaCode': area_code or '',
                    'ProvinceCode': display_province_code or '',
                    'DistrictCode': display_district_code or '',
                    'WardCode': display_ward_code or '',
                    'Address': address or '',
                    'Status': 'new',
                    'PosNumber': pos_number or '',
                    'Certificate': certificate or '',
                    'ContractNumber': contract_number or '',
                    'ContractDate': contract_date or '',
                    'UpdateContractNumber': update_contract_number or '',
                    'UpdateContractDate': update_contract_date or '',
                    'ReleaseContractNumber': release_contract_number or '',
                    'ReleaseContractDate': release_contract_date or '',
                    'TerminalCount': terminal_count or '',
                    'BranchCode': display_branch_code
                })

            return pos_list

        except Exception as e:
            logger.error(f"Error getting POS: {e}")
            return []

# Global database manager instance
db_manager = DatabaseManager()
