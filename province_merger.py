"""
Province merger functionality for Location API
Handles province merging logic based on UpdatedDate parameter
"""
import csv
import os
import re
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from database import db_manager

logger = logging.getLogger(__name__)

class ProvinceMerger:
    def __init__(self):
        self.merger_date = "20250701"  # <PERSON><PERSON><PERSON> sáp nhập
        self.csv_file = "data_import/SapNhapTinhThanh.csv"
        self.mapping = {}  # old_code -> new_code
        self.reverse_mapping = {}  # new_code -> [old_codes]
        self.name_to_code_mapping = {}  # Mapping tên -> mã
        self.load_province_codes()
        self.load_merger_mapping()
    
    def normalize_name(self, name: str) -> str:
        """Chuẩn hóa tên tỉnh thành để so sánh"""
        if not name:
            return ""
        
        # Loại bỏ các tiền tố thường gặp
        name = re.sub(r'^(Tp\.|TP\.|Thành phố|Tỉnh)\s*', '', name, flags=re.IGNORECASE)
        
        # Chuẩn hóa một số tên đặc biệt
        replacements = {
            'H<PERSON> Chí Minh': 'HCM',
            'Hà Nội': 'HN', 
            'Đà Nẵng': 'DAN',
            'Cần Thơ': 'CTH',
            'Hải Phòng': 'HP',
            'Thừa Thiên Huế': 'HUE',
            'Huế': 'HUE',
            'Bà Rịa - Vũng Tàu': 'BR',
            'Đắk Nông': 'ÐKN'
        }
        
        for old, new in replacements.items():
            if old.lower() in name.lower():
                return new
        
        # Loại bỏ dấu và khoảng trắng, chuyển thành uppercase
        name = re.sub(r'\s+', '', name)
        name = self.remove_accents(name).upper()
        
        return name
    
    def remove_accents(self, text: str) -> str:
        """Loại bỏ dấu tiếng Việt"""
        accents = {
            'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
            'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
            'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
            'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
            'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
            'đ': 'd', 'Đ': 'D'
        }
        
        for accented, plain in accents.items():
            text = text.replace(accented, plain)
            text = text.replace(accented.upper(), plain.upper())
        
        return text
    
    def load_province_codes(self):
        """Load mapping tên tỉnh -> mã tỉnh từ database"""
        try:
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT locationCode, locationName FROM locations WHERE locationType = 1')
            provinces = cursor.fetchall()
            conn.close()
            
            for code, name in provinces:
                normalized_name = self.normalize_name(name)
                self.name_to_code_mapping[normalized_name] = code
                # Thêm cả tên gốc để tăng khả năng match
                self.name_to_code_mapping[name] = code
                
            logger.info(f"Loaded {len(provinces)} province codes from database")
            
        except Exception as e:
            logger.error(f"Error loading province codes: {e}")
    
    def find_province_code(self, province_name: str) -> Optional[str]:
        """Tìm mã tỉnh từ tên tỉnh"""
        if not province_name:
            return None
            
        # Thử tìm trực tiếp
        if province_name in self.name_to_code_mapping:
            return self.name_to_code_mapping[province_name]
        
        # Thử tìm với tên chuẩn hóa
        normalized = self.normalize_name(province_name)
        if normalized in self.name_to_code_mapping:
            return self.name_to_code_mapping[normalized]
        
        # Thử tìm fuzzy matching
        for stored_name, code in self.name_to_code_mapping.items():
            if normalized.lower() in stored_name.lower() or stored_name.lower() in normalized.lower():
                return code
        
        logger.warning(f"Could not find province code for: {province_name}")
        return None
    
    def load_merger_mapping(self):
        """Load mapping sáp nhập từ CSV file với logic: cột trống = sáp nhập vào tỉnh ở trên"""
        try:
            if not os.path.exists(self.csv_file):
                logger.error(f"CSV file not found: {self.csv_file}")
                return

            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.reader(file, delimiter=';')
                next(reader)  # Skip header

                current_target = None  # Tỉnh đích hiện tại

                for row in reader:
                    if len(row) >= 2:
                        old_name = row[0].strip()
                        new_name = row[1].strip()

                        if not old_name:  # Skip empty rows
                            continue

                        if new_name:
                            # Có tỉnh đích được chỉ định
                            current_target = new_name
                            target_name = new_name
                        else:
                            # Cột trống - sáp nhập vào tỉnh ở trên
                            if current_target:
                                target_name = current_target
                            else:
                                logger.warning(f"No target found for: {old_name}")
                                continue

                        # Tìm mã tỉnh
                        old_code = self.find_province_code(old_name)
                        new_code = self.find_province_code(target_name)

                        if old_code and new_code:
                            self.mapping[old_code] = new_code

                            # Reverse mapping
                            if new_code not in self.reverse_mapping:
                                self.reverse_mapping[new_code] = []
                            if old_code not in self.reverse_mapping[new_code]:
                                self.reverse_mapping[new_code].append(old_code)

                            logger.info(f"Mapped: {old_name} ({old_code}) -> {target_name} ({new_code})")
                        else:
                            logger.warning(f"Could not map: {old_name} -> {target_name}")

            logger.info(f"Loaded {len(self.mapping)} province merger mappings")

        except Exception as e:
            logger.error(f"Error loading merger mapping: {e}")
    
    def get_merged_location_code(self, location_code: str, updated_date: Optional[str] = None) -> str:
        """
        Lấy mã tỉnh thành sau sáp nhập dựa trên ngày cập nhật
        
        Args:
            location_code: Mã tỉnh thành hiện tại
            updated_date: Ngày cập nhật (format YYYYMMDD)
            
        Returns:
            Mã tỉnh thành sau sáp nhập (nếu có) hoặc mã gốc
        """
        # Nếu không có ngày hoặc ngày >= ngày sáp nhập, trả về mã sau sáp nhập
        if not updated_date or updated_date >= self.merger_date:
            return self.mapping.get(location_code, location_code)
        
        # Nếu ngày < ngày sáp nhập, trả về mã gốc
        return location_code
    
    def should_show_location(self, location_code: str, updated_date: Optional[str] = None) -> bool:
        """
        Kiểm tra có nên hiển thị location này không dựa trên logic sáp nhập

        Args:
            location_code: Mã tỉnh thành
            updated_date: Ngày cập nhật (format YYYYMMDD)

        Returns:
            True nếu nên hiển thị, False nếu không
        """
        # Nếu không có ngày hoặc ngày >= ngày sáp nhập
        if not updated_date or updated_date >= self.merger_date:
            # Chỉ hiển thị tỉnh thành đích
            # Tỉnh thành đích là tỉnh mà mapping[x] = location_code hoặc location_code không bị sáp nhập
            merged_code = self.mapping.get(location_code, location_code)
            return merged_code == location_code

        # Nếu ngày < ngày sáp nhập, hiển thị tất cả
        return True
    
    def get_merger_info(self) -> Dict:
        """Lấy thông tin về sáp nhập"""
        return {
            "merger_date": self.merger_date,
            "total_mappings": len(self.mapping),
            "mappings": dict(self.mapping),
            "reverse_mappings": dict(self.reverse_mapping)
        }

# Global instance
province_merger = ProvinceMerger()
