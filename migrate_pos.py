#!/usr/bin/env python3
"""
Migration script to add POS mapping functionality
"""
import sqlite3
import logging
from database import db_manager
from pos_mapper import pos_mapper

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_pos_database():
    """Migrate database to support POS mapping"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # 1. Add mapped_pos_code column if not exists
        logger.info("Adding mapped_pos_code column...")
        try:
            cursor.execute('ALTER TABLE pos ADD COLUMN mapped_pos_code TEXT')
            logger.info("✅ Added mapped_pos_code column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.info("✅ mapped_pos_code column already exists")
            else:
                raise
        
        # 2. Update mapped_pos_code for all records
        logger.info("Updating mapped_pos_code for all POS records...")
        
        # Get all POS records
        cursor.execute('SELECT id, posCode FROM pos')
        pos_records = cursor.fetchall()
        
        updated_count = 0
        for pos_id, pos_code in pos_records:
            # Get mapped info
            mapped_info = pos_mapper.get_mapped_pos_info(pos_code, "20250702")  # Use future date to get mapping
            mapped_code = mapped_info.get('pos_code', pos_code) if mapped_info else pos_code
            
            # Update the record
            cursor.execute(
                'UPDATE pos SET mapped_pos_code = ? WHERE id = ?',
                (mapped_code, pos_id)
            )
            updated_count += 1
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Migration completed successfully!")
        logger.info(f"   - Updated {updated_count} POS records")
        logger.info(f"   - Applied {len(pos_mapper.mapping)} POS mappings")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

def verify_pos_migration():
    """Verify POS migration results"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # Check if column exists
        cursor.execute("PRAGMA table_info(pos)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'mapped_pos_code' not in columns:
            logger.error("❌ mapped_pos_code column not found")
            return False
        
        # Check some sample data
        cursor.execute('''
            SELECT posCode, posName, mapped_pos_code 
            FROM pos 
            ORDER BY posCode 
            LIMIT 10
        ''')
        
        logger.info("Sample POS mapping data:")
        for pos_code, pos_name, mapped_code in cursor.fetchall():
            status = "MAPPED" if pos_code != mapped_code else "UNCHANGED"
            logger.info(f"  {pos_code:<15} -> {mapped_code:<15} | {pos_name[:30]:<30} | {status}")
        
        # Count mapped POS
        cursor.execute('''
            SELECT COUNT(*) FROM pos 
            WHERE posCode != mapped_pos_code
        ''')
        mapped_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pos')
        total_pos = cursor.fetchone()[0]
        
        logger.info(f"✅ Verification completed:")
        logger.info(f"   - Total POS: {total_pos}")
        logger.info(f"   - Mapped POS: {mapped_count}")
        logger.info(f"   - Unchanged POS: {total_pos - mapped_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def import_pos_data_if_needed():
    """Import POS data if table is empty"""
    try:
        pos_count = db_manager.get_pos_count()
        if pos_count == 0:
            logger.info("POS table is empty, importing data...")
            success = db_manager.import_pos_data()
            if success:
                new_count = db_manager.get_pos_count()
                logger.info(f"✅ Imported {new_count} POS records")
                return True
            else:
                logger.error("❌ Failed to import POS data")
                return False
        else:
            logger.info(f"✅ POS table already contains {pos_count} records")
            return True
    except Exception as e:
        logger.error(f"❌ Error checking/importing POS data: {e}")
        return False

def main():
    """Main migration function"""
    logger.info("🚀 Starting POS mapping migration...")
    
    # Import POS data if needed
    if not import_pos_data_if_needed():
        logger.error("❌ Cannot proceed without POS data")
        return
    
    # Load POS mappings
    logger.info(f"📊 POS mapping info: {pos_mapper.get_mapping_info()}")
    
    # Run migration
    if migrate_pos_database():
        logger.info("✅ Migration successful, running verification...")
        if verify_pos_migration():
            logger.info("🎉 POS mapping migration completed successfully!")
        else:
            logger.error("❌ Migration verification failed")
    else:
        logger.error("❌ Migration failed")

if __name__ == "__main__":
    main()
