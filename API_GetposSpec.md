1. API																							
																							
		API									 /api/getpos 												
		Mô tả API									"Lấy thông tin điểm bán hàng 
Lấy toàn bộ dữ liệu thỏa mãn điều kiện lọc"												
																							
																							
		Method									GET												
																							
																							
2. Parameter																							
																							
		Tên thuộc tính									Kiểu dữ liệu							Bắt buộc?					Mô tả
		AccessToken									string							x					Token nhận được sau mỗi lần login
		BranchCode									string												Mã Chi nhánh cha
		AgencyCode									string												Mã Đại lý cấp cha
		SubAgencyCode									string												Mã Đầu nhánh cấp cha
		UpdatedDate									string												Ngày thay đổi, format dạng YYYYMMDD. 
																								Trường hợp có nhu cầu lấy dữ liệu thay đổi theo ngày 																								
																								Trường hợp không truyền, lấy full dữ liệu 																								
																							
																							
	Request sample:																						
		{																					
			"AccessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE1NjUwNzkxMTIsImZwdGNzIjoiRlBUQ1MifQ.ndGYfggKu8LmQ2bFrmanBcYBQ0gNsWpypd2f1M4uiIE",																				
			"BranchCode":"XXXXXXX"																				
			"AgencyCode"																				
			"SubAgencyCode":"XXXXXXX"																				
			"UpdatedDate"																				
		}																					
																							
																							
3. Validate Parameter																							
																							
		Tên parameter																	Kiểm tra điều kiện										Mô tả																			
		AccessToken																	not null, not empty, length>=6										Kiểm tra AccessTocken hợp lệ															

4.Return Value																							
																							
		Kiểu trả về																								Mô tả																						
		Json																								Kết quả trả về là 1 chuỗi Json																						
		          ErrorCode																								Mã lỗi																						
		          ErrorMsg																								Mô tả chi tiết lỗi																						
		          Data																					
					Id																					Id của dữ liệu, quản trị tại WEB.QT																						
					PosCode																					Mã Điểm bán hàng																						
					PosName																					Tên Điểm bán hàng																						
					PosStatus																					Trạng thái  ĐBH																						
					PosType																					Loại hình ĐBH																						
					AgencyCode																					Mã đại lý																						
					SubAgencyCode																					Mã Đầu nhánh (nếu có)																						
					AreaCode																					Mã Khu vực																						
					ProvinceCode																					Mã Tỉnh / thành phố																						
					DistrictCode																					Mã Quận / Huyện																						
					WardCode																					Mã Phường / xã																						
					Address																					Địa chỉ chi tiết																						
		Status																								Trạng thái cập nhật ĐBH																						
	Giá trị 																						
		'new': Thêm mới																					
		'update': Thay đổi 																					
		'inactive': Chấm dứt hoạt động																					
					PosNumber																					Số GCN ĐBH																						
					Certificate																					Quan hệ đại lý																						
					ContractNumer																					Phụ lục hợp đồng đại lý																						
					ContractDate																					Ngày ký phụ lục																						
					UpdateContractNumber																					Số Phụ lục hợp đồng bổ sung/thay thế bổ sung																						
					UpdateContractDate																					Ngày ký hợp đồng bổ sung/thay thế bổ sung																						
					ReleaseContractNumber																					Số Phụ lục hợp đồng chấm dứt/thay thế chấm dứt																						
					ReleaseContractDate																					Ngày ký hợp đồng chấm dứt/thay thế chấm dứt																						
					TerminalCount																					Thiết bị đầu cuối																						
																							
																							
	Response sample:																						
		{																					
			  "ErrorCode": "000",																				
			  "ErrorMsg": "Success",																				
			  "Data": {																				
						{																	
							Id																
							PosCode																
							PosName																
							PosType																
							AgencyCode																
							SubAgencyCode																
							AreaCode																
							ProvinceCode																
							DistrictCode																
							WardCode																
							Address																
							Status																
							PosNumber																
							Certificate																
							ContractNumer																
							ContractDate																
							UpdateContractNumber																
							UpdateContractDate																
							ReleaseContractNumber																
							ReleaseContractDate																
							TerminalCount																
						},																	
			  },																				
			}																				
																							