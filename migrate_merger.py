#!/usr/bin/env python3
"""
Migration script to add province merger functionality
"""
import sqlite3
import logging
from database import db_manager
from province_merger import province_merger

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """Migrate database to support province merger"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # 1. Add merged_location_code column if not exists
        logger.info("Adding merged_location_code column...")
        try:
            cursor.execute('ALTER TABLE locations ADD COLUMN merged_location_code TEXT')
            logger.info("✅ Added merged_location_code column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.info("✅ merged_location_code column already exists")
            else:
                raise
        
        # 2. Update merged_location_code for all records
        logger.info("Updating merged_location_code for all records...")
        
        # Get all locations
        cursor.execute('SELECT id, locationType, locationCode, parentCode FROM locations')
        locations = cursor.fetchall()
        
        updated_count = 0
        for location_id, location_type, location_code, parent_code in locations:
            merged_code = location_code
            
            if location_type == 1:  # Province
                # For provinces, use direct mapping
                merged_code = province_merger.mapping.get(location_code, location_code)
            elif location_type == 2:  # District
                # For districts, update based on parent province mapping
                if parent_code:
                    merged_parent = province_merger.mapping.get(parent_code, parent_code)
                    merged_code = location_code  # Keep district code same, but parent will change
            elif location_type == 3:  # Ward
                # For wards, similar to districts
                if parent_code:
                    # Find parent district first, then its province
                    cursor.execute('SELECT parentCode FROM locations WHERE locationCode = ? AND locationType = 2', (parent_code,))
                    district_parent = cursor.fetchone()
                    if district_parent and district_parent[0]:
                        merged_province = province_merger.mapping.get(district_parent[0], district_parent[0])
                        merged_code = location_code  # Keep ward code same
            
            # Update the record
            cursor.execute(
                'UPDATE locations SET merged_location_code = ? WHERE id = ?',
                (merged_code, location_id)
            )
            updated_count += 1
        
        # 3. Update parent codes for districts and wards based on merger
        logger.info("Updating parent codes based on merger...")
        
        # Update districts whose parent provinces were merged
        cursor.execute('''
            UPDATE locations 
            SET parentCode = (
                SELECT merged_location_code 
                FROM locations AS parent 
                WHERE parent.locationCode = locations.parentCode 
                AND parent.locationType = 1
            )
            WHERE locationType = 2 
            AND parentCode IN (SELECT locationCode FROM locations WHERE locationType = 1)
        ''')
        
        # Update wards whose parent districts' provinces were merged
        # This is more complex as we need to trace through the hierarchy
        cursor.execute('''
            UPDATE locations 
            SET parentCode = (
                CASE 
                    WHEN EXISTS (
                        SELECT 1 FROM locations AS district 
                        WHERE district.locationCode = locations.parentCode 
                        AND district.locationType = 2
                    ) THEN locations.parentCode
                    ELSE (
                        SELECT merged_location_code 
                        FROM locations AS parent 
                        WHERE parent.locationCode = locations.parentCode 
                        AND parent.locationType = 1
                    )
                END
            )
            WHERE locationType = 3
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Migration completed successfully!")
        logger.info(f"   - Updated {updated_count} location records")
        logger.info(f"   - Applied {len(province_merger.mapping)} province mappings")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify migration results"""
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # Check if column exists
        cursor.execute("PRAGMA table_info(locations)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'merged_location_code' not in columns:
            logger.error("❌ merged_location_code column not found")
            return False
        
        # Check some sample data
        cursor.execute('''
            SELECT locationCode, locationName, merged_location_code, locationType 
            FROM locations 
            WHERE locationType = 1 
            ORDER BY locationName 
            LIMIT 10
        ''')
        
        logger.info("Sample province merger data:")
        for code, name, merged_code, loc_type in cursor.fetchall():
            status = "MERGED" if code != merged_code else "UNCHANGED"
            logger.info(f"  {code:<12} -> {merged_code:<12} | {name:<20} | {status}")
        
        # Count merged provinces
        cursor.execute('''
            SELECT COUNT(*) FROM locations 
            WHERE locationType = 1 AND locationCode != merged_location_code
        ''')
        merged_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM locations WHERE locationType = 1')
        total_provinces = cursor.fetchone()[0]
        
        logger.info(f"✅ Verification completed:")
        logger.info(f"   - Total provinces: {total_provinces}")
        logger.info(f"   - Merged provinces: {merged_count}")
        logger.info(f"   - Unchanged provinces: {total_provinces - merged_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main migration function"""
    logger.info("🚀 Starting province merger migration...")
    
    # Load merger mappings first
    logger.info(f"📊 Merger info: {province_merger.get_merger_info()}")
    
    # Run migration
    if migrate_database():
        logger.info("✅ Migration successful, running verification...")
        if verify_migration():
            logger.info("🎉 Province merger migration completed successfully!")
        else:
            logger.error("❌ Migration verification failed")
    else:
        logger.error("❌ Migration failed")

if __name__ == "__main__":
    main()
